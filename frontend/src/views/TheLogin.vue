<template>
  <div class="login-container">
    <!-- 主登录卡片 -->
    <div class="login-card">
      <!-- 左右分布的主要内容区域 -->
      <div class="login-content">
        <!-- 左侧 Logo 区域 -->
        <div class="logo-section">
          <div class="logo-wrapper">
            <img src="/logo.png" alt="宿迁烟草局" class="company-logo" />
            <div class="title-section">
              <h1 class="system-title">宿迁烟草局合同审核系统</h1>
              <p class="system-subtitle">Contract Review System</p>
            </div>
          </div>
        </div>

        <!-- 右侧登录表单区域 -->
        <div class="login-form-section">
        <el-form
          ref="loginFormRef"
          :model="loginForm"
          :rules="loginRules"
          size="large"
          @submit.prevent="handleLogin"
        >
          <el-form-item prop="username">
            <el-input
              v-model="loginForm.username"
              placeholder="用户名"
              clearable
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <el-icon><User /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              type="password"
              placeholder="密码"
              show-password
              clearable
              @keyup.enter="handleLogin"
            >
              <template #prefix>
                <el-icon><Lock /></el-icon>
              </template>
            </el-input>
          </el-form-item>

          <el-form-item>
            <el-button
              type="primary"
              size="large"
              :loading="loading"
              class="login-btn"
              @click="handleLogin"
            >
              {{ loading ? "登录中..." : "登录" }}
            </el-button>
          </el-form-item>
        </el-form>

        <!-- 演示账号区域 -->
        <div class="demo-section">
          <div class="demo-header">
            <span class="demo-label">演示账号</span>
          </div>
          <div class="demo-accounts">
            <div class="demo-account" @click="fillAccount('admin', 'admin123')">
              <span class="role-badge admin">管理员</span>
            </div>
            <div
              class="demo-account"
              @click="fillAccount('county_reviewer', '123456')"
            >
              <span class="role-badge county-reviewer">县级审核员</span>
            </div>
            <div
              class="demo-account"
              @click="fillAccount('city_reviewer', '123456')"
            >
              <span class="role-badge city-reviewer">市级审核员</span>
            </div>

            <div
              class="demo-account"
              @click="fillAccount('employee', '123456')"
            >
              <span class="role-badge employee">员工</span>
            </div>

            <div
              class="demo-account"
              @click="fillAccount('legal_officer', '123456')"
            >
              <span class="role-badge legal-officer">法规员</span>
            </div>
          </div>
        </div>
        </div>
      </div>
    </div>

    <!-- 底部版权信息 -->
    <div class="footer-info">
      <p class="copyright">&copy; 2024 宿迁烟草专卖局 版权所有</p>
      <p class="version">合同审核管理系统 v1.0</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from "vue";
import { ElMessage } from "element-plus";
import { User, Lock } from "@element-plus/icons-vue";
import { useUserStore } from "@/stores/user";
import { useRouter } from "vue-router";

// 用户状态管理
const userStore = useUserStore();
const router = useRouter();

// 从store中获取状态和方法
const { login, loading, isAuthenticated } = userStore;

// 表单引用
const loginFormRef = ref();

// 登录表单数据
const loginForm = reactive({
  username: "",
  password: "",
});

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: "请输入用户名", trigger: "blur" },
    {
      min: 3,
      max: 20,
      message: "用户名长度应在3-20个字符之间",
      trigger: "blur",
    },
  ],
  password: [
    { required: true, message: "请输入密码", trigger: "blur" },
    { min: 6, message: "密码长度至少6个字符", trigger: "blur" },
  ],
};

// 处理登录
const handleLogin = async () => {
  // 验证表单
  const valid = await loginFormRef.value.validate();
  if (!valid) {
    return;
  }

  // 执行登录
  const success = await login(loginForm);

  if (success) {
    // 登录成功，等待状态更新后跳转到仪表盘
    await new Promise((resolve) => setTimeout(resolve, 200)); // 等待200ms确保状态更新
    const redirect = router.currentRoute.value.query.redirect || "/dashboard";

    // 尝试使用router.push跳转
    try {
      await router.push(redirect);
    } catch (error) {
      console.warn("路由跳转失败，使用window.location跳转:", error);
      // 如果路由跳转失败，使用window.location
      window.location.href = redirect;
    }
  } else {
  }
};

// 填充演示账号
const fillAccount = (username, password) => {
  loginForm.username = username;
  loginForm.password = password;
  ElMessage.info(`已填充${username}账号信息，点击登录即可`);
};

// 组件挂载时检查登录状态
onMounted(() => {
  if (isAuthenticated.value) {
    // 如果已经登录，跳转到工作台
    // router.push('/dashboard') // 路由跳转在 useAuth 中处理
  }
});
</script>

<style scoped>
/* 重置和基础样式 */
* {
  box-sizing: border-box;
}

/* 主容器 */
.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #409eff 0%, #79bbff 50%, #a0cfff 100%);
  padding: 20px;
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC",
    "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial,
    sans-serif;
  position: relative;
  overflow: hidden;
}

/* 添加背景装饰元素 */
.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(64, 158, 255, 0.2) 0%, transparent 60%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(121, 187, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

/* 添加浮动装饰元素 */
.login-container::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background:
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(64, 158, 255, 0.1) 0%, transparent 50%);
  animation: float 20s ease-in-out infinite;
  pointer-events: none;
}

@keyframes float {
  0%, 100% { transform: rotate(0deg) translate(0, 0); }
  33% { transform: rotate(120deg) translate(20px, -20px); }
  66% { transform: rotate(240deg) translate(-20px, 20px); }
}

/* 登录卡片 */
.login-card {
  width: 100%;
  max-width: 1000px;
  min-height: 600px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  box-shadow:
    0 20px 60px 0 rgba(31, 38, 135, 0.3),
    0 0 0 1px rgba(255, 255, 255, 0.18);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  overflow: hidden;
}

/* 左右分布的主要内容区域 */
.login-content {
  display: flex;
  height: 100%;
  min-height: 600px;
}

/* 左侧 Logo 区域 */
.logo-section {
  flex: 1;
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  position: relative;
  overflow: hidden;
}

.logo-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 70%, rgba(255, 255, 255, 0.15) 0%, transparent 60%),
    radial-gradient(circle at 70% 30%, rgba(255, 255, 255, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(121, 187, 255, 0.1) 0%, transparent 70%);
  pointer-events: none;
}

.logo-wrapper {
  text-align: center;
  position: relative;
  z-index: 1;
  animation: logoFloat 6s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.company-logo {
  height: auto;
  width: 100%;
  max-width: 320px; /* 再次增大：240px -> 320px */
  min-width: 160px;
  object-fit: contain;
  transition: all 0.4s ease;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.1));
  opacity: 0.7; /* 增加透明度，让Logo更透明 */
}

.company-logo:hover {
  opacity: 0.9; /* 悬停时稍微减少透明度 */
  transform: scale(1.02); /* 悬停时轻微放大 */
}

.system-title {
  font-size: 28px;
  font-weight: 700;
  margin: 20px 0 8px;
  color: white;
  line-height: 1.3;
  text-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  letter-spacing: 1px;
}

.system-subtitle {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
  font-weight: 400;
  letter-spacing: 2px;
  text-shadow: 0 1px 4px rgba(0, 0, 0, 0.2);
}

/* 右侧表单区域 */
.login-form-section {
  flex: 1;
  padding: 60px 50px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  background: rgba(255, 255, 255, 0.02);
}

/* 表单样式 */
.login-form-section :deep(.el-form-item) {
  margin-bottom: 24px;
}

.login-form-section :deep(.el-input__wrapper) {
  border: 2px solid rgba(64, 158, 255, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  height: 48px;
  background-color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(8px);
}

.login-form-section :deep(.el-input__wrapper:hover) {
  border-color: rgba(64, 158, 255, 0.3);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
}

.login-form-section :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 0 0 3px rgba(64, 158, 255, 0.2);
}

.login-form-section :deep(.el-input__inner) {
  font-size: 15px;
  color: #303133;
  font-weight: 500;
}

.login-form-section :deep(.el-input__prefix) {
  color: #409eff;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  height: 50px;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  border: none;
  border-radius: 12px;
  transition: all 0.3s ease;
  color: white;
  box-shadow: 0 6px 20px 0 rgba(64, 158, 255, 0.4);
  letter-spacing: 1px;
  margin-top: 10px;
  position: relative;
  overflow: hidden;
}

.login-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-btn:hover::before {
  left: 100%;
}

.login-btn:hover {
  background: linear-gradient(135deg, #79bbff 0%, #409eff 100%);
  box-shadow: 0 8px 25px 0 rgba(64, 158, 255, 0.6);
  transform: translateY(-3px);
}

.login-btn:active {
  background: linear-gradient(135deg, #337ecc 0%, #2b6cb0 100%);
  transform: translateY(-1px);
}

/* 演示账号区域 */
.demo-section {
  margin-top: 30px;
  padding-top: 24px;
  border-top: 1px solid rgba(102, 126, 234, 0.1);
}

.demo-header {
  text-align: center;
  margin-bottom: 16px;
}

.demo-label {
  font-size: 13px;
  color: #7f8c8d;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.demo-accounts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.demo-account {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px 12px;
  background: rgba(64, 158, 255, 0.05);
  border: 1px solid rgba(64, 158, 255, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.demo-account::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(64, 158, 255, 0.1), transparent);
  transition: left 0.3s;
}

.demo-account:hover::before {
  left: 100%;
}

.demo-account:hover {
  background: rgba(64, 158, 255, 0.1);
  border-color: rgba(64, 158, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
}

.role-badge {
  font-size: 12px;
  font-weight: 600;
  padding: 4px 8px;
  border-radius: 6px;
  color: white;
  letter-spacing: 0.5px;
}

.role-badge.admin {
  background: linear-gradient(135deg, #f56c6c 0%, #e53e3e 100%);
  box-shadow: 0 2px 8px rgba(245, 108, 108, 0.3);
}

.role-badge.county-reviewer {
  background: linear-gradient(135deg, #409eff 0%, #337ecc 100%);
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.3);
}

.role-badge.city-reviewer {
  background: linear-gradient(135deg, #909399 0%, #73767a 100%);
  box-shadow: 0 2px 8px rgba(144, 147, 153, 0.3);
}

.role-badge.employee {
  background: linear-gradient(135deg, #67c23a 0%, #529b2e 100%);
  box-shadow: 0 2px 8px rgba(103, 194, 58, 0.3);
}

.role-badge.legal-officer {
  background: linear-gradient(135deg, #e6a23c 0%, #cf9236 100%);
  box-shadow: 0 2px 8px rgba(230, 162, 60, 0.3);
}

/* 底部版权信息 */
.footer-info {
  margin-top: 20px;
  text-align: center;
  position: relative;
  z-index: 1;
}

.copyright {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.version {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
  margin: 0;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
/* 平板设备 */
@media (max-width: 768px) {
  .login-card {
    max-width: 90%;
    min-height: auto;
  }

  .login-content {
    flex-direction: column;
    min-height: auto;
  }

  .logo-section {
    padding: 40px 30px;
  }

  .company-logo {
    max-width: 180px;
    min-width: 120px;
  }

  .system-title {
    font-size: 24px;
  }

  .login-form-section {
    padding: 40px 30px;
  }

  .demo-accounts {
    grid-template-columns: 1fr;
  }
}

/* 小屏幕设备 */
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-card {
    max-width: 100%;
    border-radius: 12px;
  }

  .logo-section {
    padding: 30px 20px;
  }

  .company-logo {
    max-width: 150px;
    min-width: 100px;
  }

  .system-title {
    font-size: 20px;
    margin: 16px 0 6px;
  }

  .system-subtitle {
    font-size: 12px;
  }

  .login-form-section {
    padding: 30px 20px;
  }

  .login-form-section :deep(.el-input__wrapper) {
    height: 44px;
  }

  .login-btn {
    height: 46px;
    font-size: 15px;
  }

  .demo-section {
    margin-top: 20px;
    padding-top: 16px;
  }

  .footer-info {
    margin-top: 16px;
  }
}
</style>
